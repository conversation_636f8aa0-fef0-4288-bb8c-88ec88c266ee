<?php

namespace App\Console\Commands;

use App\Models\Partner;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SeedDummyData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:seed-dummy-data 
                            {--tenants=5 : Number of tenants to create}
                            {--min-users=1 : Minimum number of admin users per tenant}
                            {--max-users=3 : Maximum number of admin users per tenant}
                            {--min-partners=2 : Minimum number of partners per tenant}
                            {--max-partners=5 : Maximum number of partners per tenant}
                            {--force : Force execution without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate realistic test data for multi-tenant application including tenants, admin users, and partners';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $tenantsCount = (int) $this->option('tenants');
        $minUsers = (int) $this->option('min-users');
        $maxUsers = (int) $this->option('max-users');
        $minPartners = (int) $this->option('min-partners');
        $maxPartners = (int) $this->option('max-partners');
        $force = $this->option('force');

        // Validate input
        if ($tenantsCount < 1 || $tenantsCount > 100) {
            $this->error('Number of tenants must be between 1 and 100.');
            return self::FAILURE;
        }

        if ($minUsers > $maxUsers || $minPartners > $maxPartners) {
            $this->error('Minimum values cannot be greater than maximum values.');
            return self::FAILURE;
        }

        // Show summary
        $this->info('Dummy Data Generation Summary:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['Tenants to create', $tenantsCount],
                ['Admin users per tenant', "{$minUsers}-{$maxUsers}"],
                ['Partners per tenant', "{$minPartners}-{$maxPartners}"],
                ['Total estimated users', $tenantsCount * (($minUsers + $maxUsers) / 2)],
                ['Total estimated partners', $tenantsCount * (($minPartners + $maxPartners) / 2)],
            ]
        );

        // Confirmation
        if (!$force && !$this->confirm('Do you want to proceed with generating this test data?')) {
            $this->info('Operation cancelled.');
            return self::SUCCESS;
        }

        $this->info('Starting dummy data generation...');

        try {
            DB::beginTransaction();

            $progressBar = $this->output->createProgressBar($tenantsCount);
            $progressBar->setFormat('verbose');
            $progressBar->start();

            $totalUsers = 0;
            $totalPartners = 0;

            for ($i = 1; $i <= $tenantsCount; $i++) {
                $this->generateTenantData($i, $minUsers, $maxUsers, $minPartners, $maxPartners, $totalUsers, $totalPartners);
                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            DB::commit();

            $this->info('✅ Dummy data generation completed successfully!');
            $this->table(
                ['Type', 'Created'],
                [
                    ['Tenants', $tenantsCount],
                    ['Admin Users', $totalUsers],
                    ['Partners', $totalPartners],
                ]
            );

            $this->info('💡 Default password for all users: password');
            $this->info('💡 All tenants are active and have metadata configured');

            return self::SUCCESS;

        } catch (\Exception $e) {
            DB::rollBack();
            
            $this->error('❌ Error occurred during data generation: ' . $e->getMessage());
            Log::error('SeedDummyData command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return self::FAILURE;
        }
    }

    /**
     * Generate data for a single tenant.
     */
    private function generateTenantData(
        int $tenantNumber,
        int $minUsers,
        int $maxUsers,
        int $minPartners,
        int $maxPartners,
        int &$totalUsers,
        int &$totalPartners
    ): void {
        // Create tenant with metadata
        $tenant = Tenant::factory()
            ->withMetadata()
            ->create();

        $this->line("Created tenant #{$tenantNumber}: {$tenant->name}");

        // Create admin users for this tenant
        $usersCount = rand($minUsers, $maxUsers);
        for ($j = 1; $j <= $usersCount; $j++) {
            $user = User::factory()
                ->polish()
                ->tenantAdmin()
                ->forTenant($tenant)
                ->create();

            $totalUsers++;
        }

        // Create partners for this tenant
        $partnersCount = rand($minPartners, $maxPartners);
        $this->createPartnersForTenant($tenant, $partnersCount, $totalPartners);
    }

    /**
     * Create various types of partners for a tenant.
     */
    private function createPartnersForTenant(Tenant $tenant, int $partnersCount, int &$totalPartners): void
    {
        $partnerTypes = ['supplier', 'customer', 'business', 'individual'];
        
        for ($k = 1; $k <= $partnersCount; $k++) {
            $type = $partnerTypes[array_rand($partnerTypes)];
            
            $partner = Partner::factory()
                ->forTenant($tenant)
                ->{$type}()
                ->create();

            // Occasionally create EU or non-EU partners
            if (rand(1, 10) <= 2) { // 20% chance
                $partner = Partner::factory()
                    ->forTenant($tenant)
                    ->euPartner()
                    ->create();
            } elseif (rand(1, 10) <= 1) { // 10% chance
                $partner = Partner::factory()
                    ->forTenant($tenant)
                    ->nonEuPartner()
                    ->create();
            }

            $totalPartners++;
        }
    }
}
