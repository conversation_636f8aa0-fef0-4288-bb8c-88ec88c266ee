<?php

namespace App\Filament\App\Pages;

use App\Models\DTOTenantMetadata;
use Filament\Actions\Action;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\File;

class InvoiceConfiguration extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Konfiguracja faktur';
    protected static ?string $slug = 'invoice-configuration';
    protected static string $view = 'filament.app.pages.invoice-configuration';

    protected ?string $heading = 'Konfiguracja szablonów dokumentów sprzedaży';

    public ?array $data = [];
    public array $availableTemplates = [];
    public array $availableFields = [];
    public ?string $selectedTemplate = null;
    public ?string $specialNote = null;
    public array $selectedFields = [];

    public static function getNavigationGroup(): ?string
    {
        return __('app._.settings');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin();
    }

    public function mount(): void
    {
        $this->loadTemplateData();
        $this->loadExistingConfiguration();
        $this->data = [
            'selected_template' => $this->selectedTemplate,
            'selected_fields' => $this->selectedFields,
            'special_note' => $this->specialNote,
        ];
        $this->form->fill($this->data);
    }

    public function loadTemplateData(): void
    {
        $templatePath = resource_path('views/print/trade_docs/template.json');

        if (File::exists($templatePath)) {
            $templateData = json_decode(File::get($templatePath), true);
            $this->availableTemplates = $templateData['templates'] ?? [];
            $this->availableFields = $templateData['available_fields'] ?? [];
        } else {
            $this->availableTemplates = [];
            $this->availableFields = [];
        }
    }

    protected function loadExistingConfiguration(): void
    {
        $tenant = tenant(true);
        $tenantMeta = $tenant?->meta;

        if ($tenantMeta && $tenantMeta->meta) {
            $dto = DTOTenantMetadata::make($tenantMeta->meta);
            $invoiceConfig = $dto->getInvoiceConfiguration();
            $this->selectedTemplate = $invoiceConfig['selected_template'] ?? null;
            $this->selectedFields = $invoiceConfig['templates'][$this->selectedTemplate]['selected_fields'] ?? [];
            $this->specialNote = $invoiceConfig['special_note'] ?? '';
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Fieldset::make('Wybór szablonu')
                    ->columns(3)
                    ->schema([
                        Section::make()
                            ->columnSpan(1)
                            ->columns(1)
                            ->schema([
                                Select::make('selected_template')
                                    ->label('Szablon faktury')
                                    ->options($this->getTemplateOptions())
                                    ->placeholder('Wybierz szablon...')
                                    ->live()
                                    ->afterStateUpdated(function ($state) {
                                        $this->selectedTemplate = $state;
                                        $this->selectedFields = [];
                                        $this->dispatch('template-changed', $state);
                                    })
                                    ->helperText('Szablony dostępne w systemie'),
                                ViewField::make('template_preview')
                                    ->view('filament.app.components.template-preview')
                                    ->viewData([
                                        'template' => $this->getSelectedTemplateData(),
                                    ])
                                    ->visible(fn(Get $get) => !empty($get('selected_template'))),
                            ]),
                        CheckboxList::make('selected_fields')
                            ->label('Dodatkowe pola do wyświetlenia')
                            ->options($this->getAvailableFieldsForSelectedTemplate())
                            ->descriptions($this->getFieldDescriptions())
                            ->columns(1)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedFields = $state ?? [];
                            })
                            ->helperText('Zaznacz pola, które mają być widoczne na dokumentach')
                            ->visible(fn() => !empty($this->selectedTemplate)),
                        ViewField::make('selected_template_preview')
                            ->view('filament.app.forms.fields.invoice-image')
                            ->viewData([
                                'template' => $this->getSelectedTemplateData(),
                                'imageBase64' => $this->getTemplatePreviewImage(),
                            ])
                            ->visible(fn(Get $get) => !empty($get('selected_template'))),
                    ]),
                Fieldset::make('Specjalna notatka')
                ->columns(1)
                ->schema([
                    TextInput::make('special_note')
                        ->label('Notatka specjalna'),
                ])
            ])
            ->statePath('data');
    }

    public function getTemplateOptions(): array
    {
        $options = [];
        foreach ($this->availableTemplates as $template) {
            $options[$template['id']] = $template['template_name'] . ' (v' . $template['version'] . ')';
        }
        return $options;
    }

    public function getSelectedTemplateData(): ?array
    {
        if (empty($this->data['selected_template'])) {
            return null;
        }

        foreach ($this->availableTemplates as $template) {
            if ($template['id'] === $this->data['selected_template']) {
                return $template;
            }
        }

        return null;
    }

    public function getAvailableFieldsForSelectedTemplate(): array
    {
        if (empty($this->data['selected_template'])) {
            return [];
        }

        $templateData = $this->getSelectedTemplateData();
        if (!$templateData) {
            return [];
        }

        $supportedFields = $templateData['supported_fields'] ?? [];
        $options = [];

        foreach ($this->availableFields as $field) {
            if (in_array($field['field_name'], $supportedFields)) {
                $options[$field['field_name']] = $field['display_name'];
            }
        }

        return $options;
    }

    public function getFieldDescriptions(): array
    {
        $descriptions = [];
        foreach ($this->availableFields as $field) {
            $descriptions[$field['field_name']] = $field['description'];
        }
        return $descriptions;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('save_configuration')
                ->label('Zapisz konfigurację')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action(function () {
                    $this->saveConfiguration();
                })
                ->disabled(fn() => empty($this->selectedTemplate)),

            Action::make('refresh_templates')
                ->label('Odśwież szablony')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(function () {
                    $this->loadTemplateData();
                    Notification::make()
                        ->title('Szablony zostały odświeżone')
                        ->success()
                        ->send();
                }),
        ];
    }

    protected function saveConfiguration(): void
    {
        try {
            $tenant = tenant();
            if (!$tenant) {
                throw new \Exception('Nie można znaleźć danych firmy');
            }

            // Get current form state
            $formData = $this->form->getState();
            $this->selectedTemplate = $formData['selected_template'] ?? null;
            $this->selectedFields = $formData['selected_fields'] ?? [];

            // Prepare invoice configuration data
            $invoiceConfiguration = [
                'selected_template' => $this->selectedTemplate,
                'templates' => [],
                'special_note' => $formData['special_note'] ?? ''
            ];

            if ($this->selectedTemplate) {
                $invoiceConfiguration['templates'][$this->selectedTemplate] = [
                    'selected_fields' => $this->selectedFields,
                    'updated_at' => now()->toISOString(),
                ];
            }

            // Get existing metadata or create new
            $existingMeta = $tenant->meta?->meta ?? [];
            $dto = DTOTenantMetadata::make($existingMeta);

            // Update with new invoice configuration
            $updatedDto = $dto->updateInvoiceConfiguration($invoiceConfiguration);

            // Save to database
            $tenant->meta()->updateOrCreate(
                ['tenant_id' => $tenant->id],
                ['meta' => $updatedDto->toArray()]
            );

            $templateName = $this->getSelectedTemplateData()['template_name'] ?? 'Nieznany';
            $fieldsCount = count($this->selectedFields);
            tenant(true);

            Notification::make()
                ->title('Konfiguracja została zapisana')
                ->body("Szablon: {$templateName}, Wybrane pola: {$fieldsCount}")
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Błąd podczas zapisywania')
                ->body('Wystąpił błąd: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
