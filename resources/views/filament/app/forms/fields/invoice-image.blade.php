@if($template && isset($template['template_preview']) && isset($imageBase64) && $imageBase64)
    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
        <div class="mb-3">
            <h4 class="text-sm font-medium text-gray-900">Podgląd szablonu: {{ $template['template_name'] }}</h4>
            <p class="text-xs text-gray-500 mt-1">{{ $template['description'] }}</p>
        </div>
        <div class="flex justify-center">
            <img
                src="{{ $imageBase64 }}"
                alt="Podgląd szablonu {{ $template['template_name'] }}"
                class="max-w-full h-auto rounded-lg shadow-sm border border-gray-100"
                style="max-height: 400px; max-width: 300px;"
                loading="lazy"
            />
        </div>
        <div class="mt-3 text-xs text-gray-500">
            <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                Wersja {{ $template['version'] }}
            </span>
        </div>
    </div>
@elseif($template && isset($template['template_preview']))
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
        <div class="text-yellow-600">
            <svg class="mx-auto h-12 w-12 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-yellow-900">Brak podglądu szablonu</h3>
            <p class="mt-1 text-sm text-yellow-700">Plik podglądu {{ $template['template_preview'] }} nie został znaleziony.</p>
        </div>
    </div>
@else
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
        <div class="text-gray-500">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Brak wybranego szablonu</h3>
            <p class="mt-1 text-sm text-gray-500">Wybierz szablon z listy powyżej, aby zobaczyć jego podgląd.</p>
        </div>
    </div>
@endif
