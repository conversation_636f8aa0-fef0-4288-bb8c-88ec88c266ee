<?php

namespace Database\Factories;

use App\Enums\Roles;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    protected $model = User::class;

    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'active' => true,
            'hash' => null, // Will be auto-generated by model's save method
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Configure the factory for tenant admin user.
     */
    public function tenantAdmin(): static
    {
        return $this->afterCreating(function (User $user) {
            $user->assignRole(Roles::TENANT_ADMIN->value);
        });
    }

    /**
     * Configure the factory for employee user.
     */
    public function employee(): static
    {
        return $this->afterCreating(function (User $user) {
            $user->assignRole(Roles::EMPLOYEE->value);
        });
    }

    /**
     * Configure the factory for super admin user.
     */
    public function superAdmin(): static
    {
        return $this->afterCreating(function (User $user) {
            $user->assignRole(Roles::SUPER_ADMIN->value);
        });
    }

    /**
     * Configure the factory for a specific tenant.
     */
    public function forTenant(Tenant $tenant): static
    {
        return $this->afterCreating(function (User $user) use ($tenant) {
            // Attach user to tenant
            $user->tenant()->attach($tenant->id);
        });
    }

    /**
     * Configure the factory for inactive user.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'active' => false,
        ]);
    }

    /**
     * Configure the factory with a specific password.
     */
    public function withPassword(string $password): static
    {
        return $this->state(fn (array $attributes) => [
            'password' => Hash::make($password),
        ]);
    }

    /**
     * Configure the factory with Polish-style name and email.
     */
    public function polish(): static
    {
        return $this->state(function (array $attributes) {
            $polishFirstNames = [
                'Anna', 'Maria', 'Katarzyna', 'Małgorzata', 'Agnieszka', 'Barbara', 'Ewa', 'Krystyna',
                'Piotr', 'Krzysztof', 'Andrzej', 'Jan', 'Tomasz', 'Paweł', 'Michał', 'Marcin'
            ];

            $polishLastNames = [
                'Nowak', 'Kowalski', 'Wiśniewski', 'Dąbrowski', 'Lewandowski', 'Wójcik', 'Kamiński',
                'Kowalczyk', 'Zieliński', 'Szymański', 'Woźniak', 'Kozłowski', 'Jankowski', 'Mazur'
            ];

            $firstName = fake()->randomElement($polishFirstNames);
            $lastName = fake()->randomElement($polishLastNames);
            $fullName = $firstName . ' ' . $lastName;

            return [
                'name' => $fullName,
                'email' => strtolower($firstName . '.' . $lastName . '.' . fake()->randomNumber(3) . '@' . fake()->freeEmailDomain()),
            ];
        });
    }
}
